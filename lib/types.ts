"use client"

export type Department = {
  id: string
  name: string
}

export type Manager = {
  id: string // This would be the Clerk user_id
  fullName: string
  email?: string
  role?: UserRole
  departmentId?: string | null
}

export type Employee = {
  id: string
  fullName: string
  firstName?: string
  lastName?: string
  email?: string
  bio?: string
  linkedinUrl?: string
  twitterUrl?: string
  telegramUrl?: string
  role?: string
  compensation: "hourly" | "monthly"
  rate: number
  departmentId: string
  departmentName?: string
  managerId?: string | null // Legacy field for backward compatibility
  managerName?: string | null // Legacy field for backward compatibility
  managerIds?: string[] // New multi-manager support
  managers?: EmployeeManager[] // Full manager details
  active: boolean
  updatedAt?: string
}

export type EmployeeManager = {
  id: string
  employeeId: string
  managerId: string
  managerName?: string
  isPrimary: boolean
  assignedAt: string
}

export type EmployeeKPI = {
  id: string
  employeeId: string
  kpiName: string
  kpiValue?: string
  kpiTarget?: string
  kpiUnit?: string
  period?: string
  description?: string
  createdAt: string
  updatedAt: string
}

export type EmployeeProfile = Employee & {
  kpis?: EmployeeKPI[]
}

export type AppraisalPeriod = {
  id: string
  periodStart: string // ISO date string
  periodEnd: string // ISO date string
  closed: boolean
}

export type AppraisalStatus = "submitted" | "draft" | "not-started" | "approved" | "rejected" | "ready-to-pay" | "contact-manager"

export type EmployeeAppraisal = {
  employeeId: string
  fullName: string
  departmentName: string
  status: AppraisalStatus
  submittedAt?: string
  role?: UserRole
  isManager?: boolean
  managerName?: string
  multiManagerStatus?: MultiManagerAppraisalStatus
}

export type MultiManagerAppraisalStatus = {
  totalManagers: number
  completedManagers: number
  managersStatus: ManagerAppraisalStatus[]
  allManagersCompleted: boolean
}

export type ManagerAppraisalStatus = {
  managerId: string
  managerName: string
  status: AppraisalStatus
  submittedAt?: string
  isPrimary: boolean
}

export type UserRole = "super-admin" | "hr-admin" | "manager"

export type MockUser = {
  id: string
  fullName: string
  role: UserRole
}

export type UserSession = {
  id: string
  full_name: string
  role: UserRole
}

export type AppraisalDetails = {
  id: string
  periodId: string
  employeeId: string
  managerId: string
  q1: "below-expectations" | "meets-expectations" | "exceeds-expectations" | null
  q2: boolean
  q3: string
  q4: string
  q5: string
  status: "draft" | "submitted"
  paymentStatus?: "ready-to-pay" | "contact-manager" | null
  revisionNumber?: number
  isRevision?: boolean
  originalSubmissionDate?: string
  lastEditedAt?: string
  // New appraisal questions
  keyContributions?: string
  extraInitiatives?: string
  performanceLacking?: string
  disciplineRating?: number | null
  disciplineComment?: string
  daysOffTaken?: number | null
  impactRating?: number | null
  impactComment?: string
  qualityRating?: number | null
  qualityComment?: string
  collaborationRating?: number | null
  collaborationComment?: string
  skillGrowthRating?: number | null
  skillGrowthComment?: string
  readinessPromotion?: "strong-yes" | "yes-with-reservations" | "no-not-yet" | null
  readinessComment?: string
  compensationRecommendation?: string
}

export type EmployeeDetails = {
  id: string
  fullName: string
  departmentName: string
  compensation: "hourly" | "monthly"
  rate: number
}

export type AccountingViewData = {
  employeeId: string
  employeeName: string
  departmentName: string
  managerName: string
  status: AppraisalStatus
  submittedAt: string | null
  compensation: "hourly" | "monthly"
  rate: number
  hours: number
  paymentStatus: "ready-to-pay" | "contact-manager" | null
  totalAmount: number
  appraisalId?: string
}

export type PerformanceStats = {
  total: number
  belowExpectations: number
  meetsExpectations: number
  exceedsExpectations: number
  notStarted: number
  submittedCount: number
  draftCount: number
}

export type AccountingStats = {
  totalEmployees: number
  readyToPay: number
  contactManager: number
  totalPaymentAmount: number
  hourlyEmployees: number
  monthlyEmployees: number
  submittedAppraisals: number
  pendingAppraisals: number
}

// PTO (Paid Time Off) Types
export type PTORequestStatus = "pending" | "approved" | "rejected" | "cancelled"
export type PTORequestType = "vacation" | "sick" | "personal" | "emergency"

export type PTOBalance = {
  id: string
  employeeId: string
  year: number
  totalDays: number
  usedDays: number
  availableDays: number
  createdAt: string
  updatedAt: string
}

export type PTORequest = {
  id: string
  employeeId: string
  employeeName: string
  managerId: string
  managerName: string
  requestType: PTORequestType
  startDate: string // ISO date string
  endDate: string // ISO date string
  daysRequested: number
  reason?: string
  status: PTORequestStatus
  approvedBy?: string
  approvedAt?: string
  rejectedReason?: string
  createdAt: string
  updatedAt: string
}

export type PTORequestWithBalance = PTORequest & {
  employeeBalance: PTOBalance
}

export type PTOStats = {
  totalRequests: number
  pendingRequests: number
  approvedRequests: number
  rejectedRequests: number
}

export type PTODashboardData = {
  balance: PTOBalance
  recentRequests: PTORequest[]
  stats: PTOStats
}

// Appraisal Template Types
export type TemplateQuestionType = "text" | "textarea" | "radio" | "checkbox" | "select"

export type TemplateQuestionOption = {
  value: string
  label: string
}

export type TemplateQuestion = {
  id: string
  type: TemplateQuestionType
  question: string
  required: boolean
  placeholder?: string
  options?: TemplateQuestionOption[]
  rows?: number // For textarea
}

export type AppraisalTemplate = {
  id: string
  name: string
  description?: string
  questions: TemplateQuestion[]
  departmentId?: string
  departmentName?: string
  roleFilter?: string
  isActive: boolean
  isDefault: boolean
  version: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

export type TemplateUsage = {
  id: string
  templateId: string
  periodId: string
  managerId: string
  usageCount: number
  createdAt: string
}

// Update AppraisalDetails to include template reference
export type AppraisalDetailsWithTemplate = AppraisalDetails & {
  templateId?: string
  templateName?: string
}

export type PTOManagerDashboardData = {
  pendingRequests: PTORequest[]
  teamRequests: PTORequest[]
  stats: PTOStats
}

// Employee Feedback Types
export type FeedbackType = "complaint" | "suggestion" | "recognition" | "concern" | "initiative" | "general"
export type FeedbackCategory = "performance" | "behavior" | "communication" | "teamwork" | "leadership" | "process" | "other"
export type FeedbackPriority = "low" | "medium" | "high" | "urgent"
export type FeedbackStatus = "pending" | "under_review" | "investigating" | "resolved" | "closed" | "escalated"

export type EmployeeFeedback = {
  id: string
  submitterId: string
  submitterName?: string
  targetEmployeeId?: string | null
  targetEmployeeName?: string | null
  feedbackType: FeedbackType
  category?: FeedbackCategory | null
  subject: string
  message: string
  isAnonymous: boolean
  priority: FeedbackPriority
  status: FeedbackStatus
  hrResponse?: string | null
  hrNotes?: string | null
  reviewedBy?: string | null
  reviewedAt?: string | null
  resolutionSummary?: string | null
  resolvedBy?: string | null
  resolvedAt?: string | null
  requiresFollowup: boolean
  followupDate?: string | null
  followupNotes?: string | null
  createdAt: string
  updatedAt: string
}

export type FeedbackComment = {
  id: string
  feedbackId: string
  commenterId: string
  commenterName?: string
  comment: string
  isInternal: boolean
  createdAt: string
  updatedAt: string
}

export type FeedbackStatusHistory = {
  id: string
  feedbackId: string
  previousStatus?: string | null
  newStatus: string
  changedBy?: string | null
  changedByName?: string | null
  changeReason?: string | null
  createdAt: string
}

export type FeedbackStatistics = {
  totalFeedback: number
  pendingCount: number
  underReviewCount: number
  resolvedCount: number
  byType: Record<FeedbackType, number>
  byPriority: Record<FeedbackPriority, number>
  avgResolutionDays: number
}

export type FeedbackFormData = {
  targetEmployeeId?: string | null
  feedbackType: FeedbackType
  category?: FeedbackCategory | null
  subject: string
  message: string
  isAnonymous: boolean
  priority: FeedbackPriority
}

// Multi-Level Approval Types
export type ApprovalWorkflowType = "standard" | "manager_cascade" | "dual_manager"
export type ApprovalWorkflowStatus = "pending" | "in_progress" | "completed" | "rejected" | "cancelled"
export type ApprovalStepStatus = "pending" | "approved" | "rejected" | "skipped"
export type ApprovalStepType = "required" | "optional" | "conditional"
export type ApprovalAction = "approved" | "rejected" | "delegated" | "escalated" | "auto_approved"

export type ApprovalWorkflow = {
  id: string
  appraisalId: string
  workflowType: ApprovalWorkflowType
  currentLevel: number
  totalLevels: number
  status: ApprovalWorkflowStatus
  createdAt: string
  updatedAt: string
  completedAt?: string | null
}

export type ApprovalStep = {
  id: string
  workflowId: string
  stepLevel: number
  approverId: string
  approverRole?: string | null
  approverName?: string | null
  stepType: ApprovalStepType
  status: ApprovalStepStatus
  approvedAt?: string | null
  rejectedAt?: string | null
  rejectionReason?: string | null
  comments?: string | null
  createdAt: string
}

export type ApprovalHistory = {
  id: string
  workflowId: string
  stepId?: string | null
  action: ApprovalAction
  actorId: string
  actorName?: string | null
  previousStatus?: string | null
  newStatus?: string | null
  comments?: string | null
  metadata?: Record<string, unknown> | null
  createdAt: string
}

export type PendingApproval = {
  workflowId: string
  appraisalId: string
  employeeName: string
  departmentName: string
  stepLevel: number
  stepId: string
  submissionDate: string
  daysPending: number
}

export type ApprovalDelegation = {
  id: string
  originalApproverId: string
  delegateApproverId: string
  workflowId: string
  delegationReason?: string | null
  isActive: boolean
  createdBy: string
  createdAt: string
  expiresAt?: string | null
}

// Salary Deduction and Accounting Types
export type SalaryAdjustmentType = "deduction" | "bonus" | "none"
export type AccountingStatus = "pending" | "reviewed" | "processed" | "paid"
export type AdjustmentApprovalStatus = "pending" | "approved" | "rejected"
export type PaymentStatus = "pending" | "processing" | "completed" | "failed" | "cancelled"
export type CommentType = "general" | "deduction" | "bonus" | "correction" | "payment_issue"
export type CommentPriority = "low" | "normal" | "high" | "urgent"

export type SalaryAdjustment = {
  id: string
  appraisalId: string
  employeeId: string
  adjustmentType: SalaryAdjustmentType
  amount: number
  reason: string
  description?: string | null
  requestedBy: string
  approvedBy?: string | null
  approvalStatus: AdjustmentApprovalStatus
  approvedAt?: string | null
  rejectionReason?: string | null
  processedBy?: string | null
  processedAt?: string | null
  processingNotes?: string | null
  periodId?: string | null
  createdAt: string
  updatedAt: string
}

export type AccountingComment = {
  id: string
  appraisalId: string
  commenterId: string
  commenterName?: string | null
  commentType: CommentType
  comment: string
  isInternal: boolean
  priority: CommentPriority
  isResolved: boolean
  resolvedBy?: string | null
  resolvedAt?: string | null
  resolutionNotes?: string | null
  createdAt: string
  updatedAt: string
}

export type PaymentProcessingLog = {
  id: string
  appraisalId: string
  employeeId: string
  baseAmount: number
  adjustmentAmount: number
  finalAmount: number
  paymentMethod?: string | null
  processedBy: string
  processingDate: string
  paymentReference?: string | null
  status: PaymentStatus
  statusNotes?: string | null
  periodId?: string | null
  createdAt: string
  updatedAt: string
}

export type AccountingSummary = {
  totalAppraisals: number
  pendingReview: number
  reviewedCount: number
  processedCount: number
  totalAdjustments: number
  totalDeductions: number
  totalBonuses: number
}

export type AppraisalForAccountingReview = {
  appraisalId: string
  employeeName: string
  departmentName: string
  managerName?: string | null
  submittedDate: string
  paymentStatus?: string | null
  currentAdjustmentAmount?: number | null
  currentAdjustmentType?: SalaryAdjustmentType | null
  daysPending: number
}
