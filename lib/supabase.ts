import { createClient } from '@supabase/supabase-js'

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Validate environment variables
if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}
if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}
if (!supabaseServiceRoleKey) {
  throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')
}

// Database types (based on the schema)
export interface Database {
  public: {
    Tables: {
      appy_departments: {
        Row: {
          id: string
          name: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
        }
      }
      appy_employees: {
        Row: {
          id: string
          full_name: string
          first_name: string | null
          last_name: string | null
          email: string | null
          role: string | null
          bio: string | null
          linkedin_url: string | null
          twitter_url: string | null
          telegram_url: string | null
          compensation: number
          rate: 'hourly' | 'monthly'
          department_id: string | null
          manager_id: string | null
          active: boolean
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          full_name: string
          first_name?: string | null
          last_name?: string | null
          email?: string | null
          role?: string | null
          bio?: string | null
          linkedin_url?: string | null
          twitter_url?: string | null
          telegram_url?: string | null
          compensation: number
          rate: 'hourly' | 'monthly'
          department_id?: string | null
          manager_id?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          full_name?: string
          first_name?: string | null
          last_name?: string | null
          email?: string | null
          role?: string | null
          bio?: string | null
          linkedin_url?: string | null
          twitter_url?: string | null
          telegram_url?: string | null
          compensation?: number
          rate?: 'hourly' | 'monthly'
          department_id?: string | null
          manager_id?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string | null
        }
      }
      appy_managers: {
        Row: {
          user_id: string
          full_name: string
          email: string
          department_id: string | null
          active: boolean
          super_admin: boolean
          created_at: string
        }
        Insert: {
          user_id: string
          full_name: string
          email: string
          department_id?: string | null
          active?: boolean
          super_admin?: boolean
          created_at?: string
        }
        Update: {
          user_id?: string
          full_name?: string
          email?: string
          department_id?: string | null
          active?: boolean
          super_admin?: boolean
          created_at?: string
        }
      }
      appy_user_roles: {
        Row: {
          user_id: string
          role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
        }
        Insert: {
          user_id: string
          role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
        }
        Update: {
          user_id?: string
          role?: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
        }
      }
      appy_appraisal_periods: {
        Row: {
          id: string
          name: string
          start_date: string
          end_date: string
          status: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          start_date: string
          end_date: string
          status?: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          start_date?: string
          end_date?: string
          status?: string
          created_at?: string
        }
      }
      appy_appraisals: {
        Row: {
          id: string
          employee_id: string
          period_id: string
          manager_id: string
          question_1: string | null
          question_2: string | null
          question_3: string | null
          question_4: string | null
          question_5: string | null
          status: 'pending' | 'submitted' | 'approved' | 'ready-to-pay' | 'contact-manager'
          payment_status: 'ready-to-pay' | 'contact-manager' | null
          submitted_at: string | null
          template_id: string | null
          revision_number: number
          is_revision: boolean
          original_submission_date: string | null
          last_edited_at: string
          created_at: string
          // New appraisal fields
          key_contributions: string | null
          extra_initiatives: string | null
          performance_lacking: string | null
          discipline_rating: number | null
          discipline_comment: string | null
          days_off_taken: number | null
          impact_rating: number | null
          impact_comment: string | null
          quality_rating: number | null
          quality_comment: string | null
          collaboration_rating: number | null
          collaboration_comment: string | null
          skill_growth_rating: number | null
          skill_growth_comment: string | null
          readiness_promotion: 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null
          readiness_comment: string | null
          compensation_recommendation: string | null
        }
        Insert: {
          id?: string
          employee_id: string
          period_id: string
          manager_id: string
          question_1?: string | null
          question_2?: string | null
          question_3?: string | null
          question_4?: string | null
          question_5?: string | null
          status?: 'pending' | 'submitted' | 'approved' | 'ready-to-pay' | 'contact-manager'
          payment_status?: 'ready-to-pay' | 'contact-manager' | null
          submitted_at?: string | null
          template_id?: string | null
          revision_number?: number
          is_revision?: boolean
          original_submission_date?: string | null
          last_edited_at?: string
          created_at?: string
          // New appraisal fields
          key_contributions?: string | null
          extra_initiatives?: string | null
          performance_lacking?: string | null
          discipline_rating?: number | null
          discipline_comment?: string | null
          days_off_taken?: number | null
          impact_rating?: number | null
          impact_comment?: string | null
          quality_rating?: number | null
          quality_comment?: string | null
          collaboration_rating?: number | null
          collaboration_comment?: string | null
          skill_growth_rating?: number | null
          skill_growth_comment?: string | null
          readiness_promotion?: 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null
          readiness_comment?: string | null
          compensation_recommendation?: string | null
        }
        Update: {
          id?: string
          employee_id?: string
          period_id?: string
          manager_id?: string
          question_1?: string | null
          question_2?: string | null
          question_3?: string | null
          question_4?: string | null
          question_5?: string | null
          status?: 'pending' | 'submitted' | 'approved' | 'ready-to-pay' | 'contact-manager'
          payment_status?: 'ready-to-pay' | 'contact-manager' | null
          submitted_at?: string | null
          template_id?: string | null
          revision_number?: number
          is_revision?: boolean
          original_submission_date?: string | null
          last_edited_at?: string
          created_at?: string
          // New appraisal fields
          key_contributions?: string | null
          extra_initiatives?: string | null
          performance_lacking?: string | null
          discipline_rating?: number | null
          discipline_comment?: string | null
          days_off_taken?: number | null
          impact_rating?: number | null
          impact_comment?: string | null
          quality_rating?: number | null
          quality_comment?: string | null
          collaboration_rating?: number | null
          collaboration_comment?: string | null
          skill_growth_rating?: number | null
          skill_growth_comment?: string | null
          readiness_promotion?: 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null
          readiness_comment?: string | null
          compensation_recommendation?: string | null
        }
      }
      appy_appraisal_templates: {
        Row: {
          id: string
          name: string
          description: string | null
          questions: any // JSONB
          department_id: string | null
          role_filter: string | null
          is_active: boolean
          is_default: boolean
          version: number
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          questions: any // JSONB
          department_id?: string | null
          role_filter?: string | null
          is_active?: boolean
          is_default?: boolean
          version?: number
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          questions?: any // JSONB
          department_id?: string | null
          role_filter?: string | null
          is_active?: boolean
          is_default?: boolean
          version?: number
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      appy_template_usage: {
        Row: {
          id: string
          template_id: string
          period_id: string
          manager_id: string
          usage_count: number
          created_at: string
        }
        Insert: {
          id?: string
          template_id: string
          period_id: string
          manager_id: string
          usage_count?: number
          created_at?: string
        }
        Update: {
          id?: string
          template_id?: string
          period_id?: string
          manager_id?: string
          usage_count?: number
          created_at?: string
        }
      }
      appy_employee_managers: {
        Row: {
          id: string
          employee_id: string
          manager_id: string
          is_primary: boolean
          assigned_at: string
          created_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          manager_id: string
          is_primary?: boolean
          assigned_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          manager_id?: string
          is_primary?: boolean
          assigned_at?: string
          created_at?: string
        }
      }
      appy_employee_kpis: {
        Row: {
          id: string
          employee_id: string
          kpi_name: string
          kpi_value: string | null
          kpi_target: string | null
          kpi_unit: string | null
          period: string | null
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          kpi_name: string
          kpi_value?: string | null
          kpi_target?: string | null
          kpi_unit?: string | null
          period?: string | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          kpi_name?: string
          kpi_value?: string | null
          kpi_target?: string | null
          kpi_unit?: string | null
          period?: string | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      appy_employee_feedback: {
        Row: {
          id: string
          submitter_id: string
          target_employee_id: string | null
          feedback_type: 'complaint' | 'suggestion' | 'recognition' | 'concern' | 'initiative' | 'general'
          category: 'performance' | 'behavior' | 'communication' | 'teamwork' | 'leadership' | 'process' | 'other' | null
          subject: string
          message: string
          is_anonymous: boolean
          priority: 'low' | 'medium' | 'high' | 'urgent'
          status: 'pending' | 'under_review' | 'investigating' | 'resolved' | 'closed' | 'escalated'
          hr_response: string | null
          hr_notes: string | null
          reviewed_by: string | null
          reviewed_at: string | null
          resolution_summary: string | null
          resolved_by: string | null
          resolved_at: string | null
          requires_followup: boolean
          followup_date: string | null
          followup_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          submitter_id: string
          target_employee_id?: string | null
          feedback_type: 'complaint' | 'suggestion' | 'recognition' | 'concern' | 'initiative' | 'general'
          category?: 'performance' | 'behavior' | 'communication' | 'teamwork' | 'leadership' | 'process' | 'other' | null
          subject: string
          message: string
          is_anonymous?: boolean
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          status?: 'pending' | 'under_review' | 'investigating' | 'resolved' | 'closed' | 'escalated'
          hr_response?: string | null
          hr_notes?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          resolution_summary?: string | null
          resolved_by?: string | null
          resolved_at?: string | null
          requires_followup?: boolean
          followup_date?: string | null
          followup_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          submitter_id?: string
          target_employee_id?: string | null
          feedback_type?: 'complaint' | 'suggestion' | 'recognition' | 'concern' | 'initiative' | 'general'
          category?: 'performance' | 'behavior' | 'communication' | 'teamwork' | 'leadership' | 'process' | 'other' | null
          subject?: string
          message?: string
          is_anonymous?: boolean
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          status?: 'pending' | 'under_review' | 'investigating' | 'resolved' | 'closed' | 'escalated'
          hr_response?: string | null
          hr_notes?: string | null
          reviewed_by?: string | null
          reviewed_at?: string | null
          resolution_summary?: string | null
          resolved_by?: string | null
          resolved_at?: string | null
          requires_followup?: boolean
          followup_date?: string | null
          followup_notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      appy_feedback_comments: {
        Row: {
          id: string
          feedback_id: string
          commenter_id: string
          comment: string
          is_internal: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          feedback_id: string
          commenter_id: string
          comment: string
          is_internal?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          feedback_id?: string
          commenter_id?: string
          comment?: string
          is_internal?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      appy_feedback_status_history: {
        Row: {
          id: string
          feedback_id: string
          previous_status: string | null
          new_status: string
          changed_by: string | null
          change_reason: string | null
          created_at: string
        }
        Insert: {
          id?: string
          feedback_id: string
          previous_status?: string | null
          new_status: string
          changed_by?: string | null
          change_reason?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          feedback_id?: string
          previous_status?: string | null
          new_status?: string
          changed_by?: string | null
          change_reason?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      appy_appraisal_status: 'pending' | 'submitted' | 'approved' | 'ready-to-pay' | 'contact-manager'
      appy_user_role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
      appy_compensation_rate: 'hourly' | 'monthly'
    }
  }
}

// Create client instances
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)
export const supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceRoleKey)

// Helper function to get the appropriate client
export function getSupabaseClient(useServiceRole = false) {
  return useServiceRole ? supabaseAdmin : supabase
}

// Database query helpers
export const supabaseQuery = {
  // Departments
  departments: () => supabase.from('appy_departments'),
  
  // Employees  
  employees: () => supabase.from('appy_employees'),
  
  // Managers
  managers: () => supabase.from('appy_managers'),
  
  // User roles
  userRoles: () => supabase.from('appy_user_roles'),
  
  // Appraisal periods
  appraisalPeriods: () => supabase.from('appy_appraisal_periods'),
  
  // Appraisals
  appraisals: () => supabase.from('appy_appraisals'),
  
  // Employee managers
  employeeManagers: () => supabase.from('appy_employee_managers'),
  
  // Employee KPIs
  employeeKpis: () => supabase.from('appy_employee_kpis'),

  // Employee Feedback
  employeeFeedback: () => supabase.from('appy_employee_feedback'),
  feedbackComments: () => supabase.from('appy_feedback_comments'),
  feedbackStatusHistory: () => supabase.from('appy_feedback_status_history'),
}

// Admin query helpers (for server-side operations)
export const supabaseAdminQuery = {
  // Departments
  departments: () => supabaseAdmin.from('appy_departments'),
  
  // Employees  
  employees: () => supabaseAdmin.from('appy_employees'),
  
  // Managers
  managers: () => supabaseAdmin.from('appy_managers'),
  
  // User roles
  userRoles: () => supabaseAdmin.from('appy_user_roles'),
  
  // Appraisal periods
  appraisalPeriods: () => supabaseAdmin.from('appy_appraisal_periods'),
  
  // Appraisals
  appraisals: () => supabaseAdmin.from('appy_appraisals'),
  
  // Employee managers
  employeeManagers: () => supabaseAdmin.from('appy_employee_managers'),
  
  // Employee KPIs
  employeeKpis: () => supabaseAdmin.from('appy_employee_kpis'),

  // Employee Feedback
  employeeFeedback: () => supabaseAdmin.from('appy_employee_feedback'),
  feedbackComments: () => supabaseAdmin.from('appy_feedback_comments'),
  feedbackStatusHistory: () => supabaseAdmin.from('appy_feedback_status_history'),
}

// Types for convenience
export type Department = Database['public']['Tables']['appy_departments']['Row']
export type Employee = Database['public']['Tables']['appy_employees']['Row']
export type Manager = Database['public']['Tables']['appy_managers']['Row']
export type UserRole = Database['public']['Tables']['appy_user_roles']['Row']
export type AppraisalPeriod = Database['public']['Tables']['appy_appraisal_periods']['Row']
export type Appraisal = Database['public']['Tables']['appy_appraisals']['Row']
export type EmployeeManager = Database['public']['Tables']['appy_employee_managers']['Row']
export type EmployeeKPI = Database['public']['Tables']['appy_employee_kpis']['Row']
export type EmployeeFeedback = Database['public']['Tables']['appy_employee_feedback']['Row']
export type FeedbackComment = Database['public']['Tables']['appy_feedback_comments']['Row']
export type FeedbackStatusHistory = Database['public']['Tables']['appy_feedback_status_history']['Row']

// console.log('📊 Supabase clients initialized successfully')