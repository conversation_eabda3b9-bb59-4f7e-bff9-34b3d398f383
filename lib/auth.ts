import { auth, currentUser } from '@clerk/nextjs/server'
import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import type { UserRole } from './schemas'
import { supabaseAdmin } from './supabase'

// Get current user with role information
export async function getCurrentUser() {
  const { userId } = await auth()

  if (!userId) {
    console.log('🚫 [DEBUG] No userId found in auth()')
    return null
  }

  const user = await currentUser()

  if (!user) {
    console.log('🚫 [DEBUG] No user found from currentUser()')
    return null
  }

  const userEmail = user.emailAddresses[0]?.emailAddress
  const fullName = `${user.firstName} ${user.lastName}`.trim()

  console.log('👤 [DEBUG] Current user from Clerk:', {
    id: userId,
    email: userEmail,
    fullName,
    firstName: user.firstName,
    lastName: user.lastName
  })

  // First, try to sync/update user in database based on email
  let databaseUserId = userId
  let role: UserRole = 'manager'

  if (userEmail) {
    console.log('🔄 [DEBUG] Syncing user with database based on email:', userEmail)

    // Check if user exists in database by email
    const { data: existingManager } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id, full_name')
      .eq('email', userEmail)
      .single()

    if (existingManager) {
      console.log('📧 [DEBUG] Found existing manager by email:', existingManager)

      // If the user_id doesn't match the Clerk ID, update it
      if (existingManager.user_id !== userId) {
        console.log('🔄 [DEBUG] Updating user_id from', existingManager.user_id, 'to', userId)

        // Update the user_id to match Clerk ID
        await supabaseAdmin
          .from('appy_managers')
          .update({
            user_id: userId,
            full_name: fullName // Also update name in case it changed
          })
          .eq('email', userEmail)

        // Update user_id in roles table too
        await supabaseAdmin
          .from('appy_user_roles')
          .update({ user_id: userId })
          .eq('user_id', existingManager.user_id)
      }

      databaseUserId = userId // Use the Clerk ID as the canonical ID
    } else {
      console.log('➕ [DEBUG] No existing manager found, creating new one')
      // Create new manager entry
      await supabaseAdmin
        .from('appy_managers')
        .insert({
          user_id: userId,
          full_name: fullName,
          email: userEmail,
          active: true
        })
    }
  }

  // Check if user is super admin from appy_managers table
  const { data: managerData, error: managerError } = await supabaseAdmin
    .from('appy_managers')
    .select('super_admin')
    .eq('user_id', databaseUserId)
    .single()

  console.log('🔍 [AUTH] Manager super_admin query:', {
    userId: databaseUserId,
    result: managerData,
    error: managerError
  })

  if (managerData?.super_admin) {
    role = 'super-admin'
    console.log('🔍 [AUTH] User is super admin from appy_managers table')
  } else {
    // Check if user has hr-admin or accountant role in appy_user_roles table as fallback
    const { data: userRole, error: roleError } = await supabaseAdmin
      .from('appy_user_roles')
      .select('role')
      .eq('user_id', databaseUserId)
      .single()

    if (userRole?.role === 'hr-admin') {
      role = 'hr-admin'
      console.log('🔍 [AUTH] User is hr-admin from appy_user_roles table')
    } else if (userRole?.role === 'accountant') {
      role = 'accountant'
      console.log('🔍 [AUTH] User is accountant from appy_user_roles table')
    } else {
      role = 'manager' // Default role for all managers
      console.log('🔍 [AUTH] User is regular manager')
    }
  }

  console.log('✅ [DEBUG] Final user data:', {
    id: databaseUserId,
    email: userEmail,
    fullName,
    role
  })

  return {
    id: databaseUserId,
    email: userEmail || '',
    fullName,
    role,
    imageUrl: user.imageUrl,
  }
}

// Get user role from headers (set by middleware)
export async function getUserRoleFromHeaders(): Promise<UserRole> {
  const headersList = await headers()
  const role = headersList.get('x-user-role') as UserRole
  return role || 'manager'
}

// Get user ID from headers (set by middleware)
export async function getUserIdFromHeaders(): Promise<string | null> {
  const headersList = await headers()
  return headersList.get('x-user-id')
}

// Check if user is super-admin
export function isSuperAdmin(userRole: UserRole): boolean {
  return userRole === 'super-admin'
}

// Check if user has super-admin access (should bypass all restrictions)
export function hasSuperAdminAccess(user: { role: UserRole } | null): boolean {
  return user?.role === 'super-admin'
}

// Check if user has specific permission
export function hasPermission(userRole: UserRole, permission: string): boolean {
  const rolePermissions = {
    'super-admin': ['*'],
    'hr-admin': [
      'employee:read', 'employee:write', 'employee:delete',
      'department:read', 'department:write', 'department:delete',
      'period:read', 'period:write', 'period:delete',
      'appraisal:read', 'appraisal:write', 'appraisal:approve',
      'approval:read',
      'pto:read', 'pto:write', 'pto:approve'
    ],
    'manager': [
      'employee:read',
      'appraisal:read', 'appraisal:write',
      'approval:read',
      'pto:read', 'pto:write', 'pto:approve'
    ],
    'accountant': [
      'employee:read',
      'appraisal:read',
      'approval:read', 'approval:export',
      'pto:read'
    ]
  }

  const permissions = rolePermissions[userRole] || []
  return permissions.includes('*') || permissions.includes(permission)
}

// Require authentication (redirect if not authenticated)
export async function requireAuth() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/sign-in')
  }
  
  return user
}

// Require specific role (redirect if insufficient permissions)
export async function requireRole(requiredRole: UserRole | UserRole[]) {
  const user = await requireAuth()
  
  const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
  
  if (!allowedRoles.includes(user.role)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return user
}

// Require specific permission (redirect if insufficient permissions)
export async function requirePermission(permission: string) {
  const user = await requireAuth()
  
  if (!hasPermission(user.role, permission)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return user
}

// Check if current user has super-admin access
export async function isCurrentUserSuperAdmin(): Promise<boolean> {
  const user = await getCurrentUser()
  return hasSuperAdminAccess(user)
}

// Check if current user can access employee data
export async function canAccessEmployee(employeeId: string): Promise<boolean> {
  const user = await getCurrentUser()
  
  if (!user) return false
  
  // Super admin and HR admin can access all employees
  if (user.role === 'super-admin' || user.role === 'hr-admin') {
    return true
  }
  
  // Managers can only access their assigned employees
  if (user.role === 'manager') {
    // TODO: Check if this employee is assigned to this manager
    // This would require a database query to check employee_assignments table
    return true // For now, allow all managers to access all employees
  }

  // Accountants can read employee data for approvals
  if (user.role === 'accountant') {
    return true
  }
  
  return false
}

// Validate user session and return user info
export async function validateSession() {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      throw new Error('No user session found')
    }
    
    const user = await currentUser()
    
    if (!user) {
      throw new Error('User not found')
    }
    
    return {
      userId,
      email: user.emailAddresses[0]?.emailAddress,
      fullName: `${user.firstName} ${user.lastName}`.trim(),
      role: (user.publicMetadata?.role as UserRole) || 'manager',
      isActive: true,
    }
  } catch (error) {
    console.error('Session validation failed:', error)
    throw new Error('Invalid session')
  }
}

// Rate limiting helper
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  userId: string, 
  action: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): boolean {
  const key = `${userId}:${action}`
  const now = Date.now()
  const userLimit = rateLimitMap.get(key)
  
  if (!userLimit || now > userLimit.resetTime) {
    // Reset or initialize rate limit
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (userLimit.count >= maxRequests) {
    return false // Rate limit exceeded
  }
  
  userLimit.count++
  return true
}

// Audit logging helper
export async function logUserAction(
  action: string, 
  details?: Record<string, any>
) {
  const user = await getCurrentUser()
  
  if (!user) return
  
  const logEntry = {
    userId: user.id,
    userRole: user.role,
    action,
    details,
    timestamp: new Date().toISOString(),
    ip: (await headers()).get('x-forwarded-for') || 'unknown',
    userAgent: (await headers()).get('user-agent') || 'unknown',
  }
  
  // In production, send this to your logging service
  console.log('User Action:', logEntry)
  
  // TODO: Store in database or send to external logging service
}

// Check if user can approve appraisals
export function canApproveAppraisals(userRole: UserRole): boolean {
  return userRole === 'super-admin' || userRole === 'hr-admin'
}

// Check if user can approve specific appraisal based on role hierarchy
export async function canApproveAppraisal(appraisalSubmittedBy: UserRole): Promise<boolean> {
  const currentUser = await getCurrentUser()
  if (!currentUser) return false

  // Super admin can approve everyone's appraisals
  if (currentUser.role === 'super-admin') {
    return true
  }

  // HR admin can approve appraisals from managers
  if (currentUser.role === 'hr-admin') {
    return appraisalSubmittedBy === 'manager'
  }

  return false
}

// Get approval permission level for current user
export async function getApprovalLevel(): Promise<'super-admin' | 'hr-admin' | 'none'> {
  const user = await getCurrentUser()
  if (!user) return 'none'

  if (user.role === 'super-admin') return 'super-admin'
  if (user.role === 'hr-admin') return 'hr-admin'
  
  return 'none'
}

// Check if user requires approval for their appraisals
export function requiresApproval(userRole: UserRole): boolean {
  // Everyone except super-admin requires approval
  return userRole !== 'super-admin'
}

export type AuthUser = Awaited<ReturnType<typeof getCurrentUser>>
export type SessionUser = Awaited<ReturnType<typeof validateSession>>
