"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { AlertTriangle, Loader, Check } from "lucide-react"
import { toast } from "sonner"
import type { Employee, Department, Manager, UserSession } from "@/lib/types"
import { employeeFormSchema, type Employee as EmployeeSchema } from "@/lib/schemas"
import { saveEmployeeAction } from "@/lib/actions"
import { useTransition } from "react"

interface EmployeeFormDialogProps {
  isOpen: boolean
  onClose: () => void
  employee: Employee | null
  departments: Department[]
  managers: Manager[]
  user?: UserSession | null
}

type EmployeeFormData = Omit<EmployeeSchema, 'id'>

export function EmployeeFormDialog({ isOpen, onClose, employee, departments, managers, user }: EmployeeFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)
  const [isSuccess, setIsSuccess] = React.useState(false)

  // Hide compensation rates from all users
  const canViewRates = false

  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      role: "",
      linkedinUrl: "",
      twitterUrl: "",
      telegramUrl: "",
      departmentId: "",
      managerId: "none",
      compensation: "monthly",
      rate: 0,
      active: true,
    },
  })

  // Reset form when dialog opens/closes or employee changes
  React.useEffect(() => {
    if (isOpen) {
      setSubmitError(null)
      setIsSuccess(false)
      if (employee) {
        form.reset({
          fullName: employee.fullName,
          email: employee.email || "",
          role: employee.role || "",
          linkedinUrl: employee.linkedinUrl || "",
          twitterUrl: employee.twitterUrl || "",
          telegramUrl: employee.telegramUrl || "",
          departmentId: employee.departmentId,
          managerId: employee.managerId || "none",
          compensation: employee.compensation,
          rate: employee.rate,
          active: employee.active,
        })
      } else {
        form.reset({
          fullName: "",
          email: "",
          role: "",
          linkedinUrl: "",
          twitterUrl: "",
          telegramUrl: "",
          departmentId: "",
          managerId: "none",
          compensation: "monthly",
          rate: 0,
          active: true,
        })
      }
    }
  }, [employee, isOpen, form])

  const onSubmit = async (data: EmployeeFormData) => {
    setSubmitError(null)

    startTransition(async () => {
      try {
        // Create FormData for server action
        const formData = new FormData()
        if (employee?.id) {
          formData.append('id', employee.id)
        }
        formData.append('fullName', data.fullName)
        formData.append('email', data.email)
        formData.append('role', data.role || '')
        formData.append('linkedinUrl', data.linkedinUrl || '')
        formData.append('twitterUrl', data.twitterUrl || '')
        formData.append('telegramUrl', data.telegramUrl || '')
        formData.append('departmentId', data.departmentId)
        formData.append('managerId', data.managerId === "none" ? '' : (data.managerId || ''))
        
        // Only update compensation data if user has permission
        if (canViewRates) {
          formData.append('compensation', data.compensation)
          formData.append('rate', data.rate.toString())
        } else if (employee) {
          // Preserve existing compensation data for editing users who can't view rates
          formData.append('compensation', employee.compensation)
          formData.append('rate', employee.rate.toString())
        } else {
          // Default values for new employees when user can't set rates
          formData.append('compensation', 'monthly')
          formData.append('rate', '0')
        }
        
        formData.append('active', data.active.toString())

        const result = await saveEmployeeAction(formData)

        if (result.success) {
          setIsSuccess(true)
          const actionType = employee ? "updated" : "added"
          toast.success(`Employee ${actionType} successfully!`, {
            description: `${data.fullName} has been ${actionType} to the system.`
          })
          
          // Brief delay to show success state before closing
          setTimeout(() => {
            onClose()
          }, 1500)
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to save employee')
          toast.error('Failed to save employee', {
            description: 'error' in result ? result.error : 'An error occurred while saving the employee.'
          })
        }
      } catch (error) {
        console.error('Form submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
        toast.error('Unexpected error', {
          description: 'An unexpected error occurred. Please try again.'
        })
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{employee ? "Edit Employee" : "Add New Employee"}</DialogTitle>
          <DialogDescription>
            {employee ? "Make changes to the employee profile." : "Add a new employee to the system."}
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter employee's full name"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter employee's email address"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role/Position</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g. Software Engineer, Marketing Manager"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="linkedinUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>LinkedIn URL (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://linkedin.com/in/username"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="twitterUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Twitter/X URL (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://twitter.com/username"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="telegramUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telegram URL (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://t.me/username"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="departmentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isPending}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a department" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="managerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Manager (Optional)</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value === "none" ? null : value)}
                    value={field.value || "none"}
                    disabled={isPending}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Assign a manager" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">No manager assigned</SelectItem>
                      {managers.map((manager) => (
                        <SelectItem key={manager.id} value={manager.id}>
                          {manager.fullName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {canViewRates && (
              <>
                <FormField
                  control={form.control}
                  name="compensation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Compensation Type</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="flex space-x-4"
                          disabled={isPending}
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="monthly" id="monthly" />
                            <Label htmlFor="monthly">Monthly Salary</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="hourly" id="hourly" />
                            <Label htmlFor="hourly">Hourly Rate</Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Rate (USD {form.watch('compensation') === 'hourly' ? 'per hour' : 'per month'})
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter rate"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                          disabled={isPending}
                          min="0"
                          step="0.01"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || !form.formState.isValid || isSuccess}
                className={isSuccess ? "bg-green-600 hover:bg-green-600" : ""}
              >
                {isSuccess ? (
                  <>
                    <Check className="mr-2 h-4 w-4 animate-pulse" />
                    Success!
                  </>
                ) : isPending ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  employee ? "Update Employee" : "Create Employee"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
